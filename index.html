<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <title>ListenMarkt</title>
    <!-- Materialize CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <?!= include('styles'); ?>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="teal lighten-2">
      <div class="nav-wrapper container">
        <a href="javascript:void(0)" onclick="navigateToListe('plakate')" class="brand-logo white-text">ListenMarkt</a>
        <a href="#" data-target="mobile-nav" class="sidenav-trigger white-text"><i class="material-icons">menu</i></a>
        <ul class="right hide-on-med-and-down">
          <!-- Vor dem Markt -->
          <li><a class="dropdown-trigger white-text" href="#!" data-target="dropdown-vor-markt">Vor dem Markt<i class="material-icons right">arrow_drop_down</i></a></li>
          <!-- Während des Markts -->
          <li><a class="dropdown-trigger white-text" href="#!" data-target="dropdown-waehrend-markt">Während des Markts<i class="material-icons right">arrow_drop_down</i></a></li>
          <!-- Sonstige Infos -->
          <li><a class="dropdown-trigger white-text" href="#!" data-target="dropdown-sonstige">Sonstige Infos<i class="material-icons right">arrow_drop_down</i></a></li>
        </ul>
      </div>
    </nav>

    <!-- Dropdown Menüs -->
    <ul id="dropdown-vor-markt" class="dropdown-content">
      <li><a href="javascript:void(0)" onclick="navigateToListe('plakate')">Plakate</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('kuchen')">Kuchenspenden</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('aufbau')">Aufbauhelfer</a></li>
    </ul>

    <ul id="dropdown-waehrend-markt" class="dropdown-content">
      <li><a href="javascript:void(0)" onclick="navigateToListe('bar')">Bar</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('pommes')">Pommes</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('spuel')">Spülen</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('kindertoepfern')">Kindertöpfern (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('lebendwerkstatt')">Lebendwerkstatt (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('siebdruck')">Siebdruck (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('ausstellung')">Ausstellungsaufsicht</a></li>
    </ul>

    <ul id="dropdown-sonstige" class="dropdown-content">
      <li><a href="javascript:void(0)" onclick="navigateToListe('lineup')">LineUp</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('zustaendige')">Zuständige Personen</a></li>
    </ul>

    <!-- Mobile Navigation -->
    <ul class="sidenav" id="mobile-nav">
      <li><h5 class="center-align teal-text">Vor dem Markt</h5></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('plakate')">Plakate</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('kuchen')">Kuchenspenden</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('aufbau')">Aufbauhelfer</a></li>
      <li><div class="divider"></div></li>
      <li><h5 class="center-align teal-text">Während des Markts</h5></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('bar')">Bar</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('pommes')">Pommes</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('spuel')">Spülen</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('kindertoepfern')">Kindertöpfern (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('lebendwerkstatt')">Lebendwerkstatt (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('siebdruck')">Siebdruck (KSL)</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('ausstellung')">Ausstellungsaufsicht</a></li>
      <li><div class="divider"></div></li>
      <li><h5 class="center-align teal-text">Sonstige Infos</h5></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('lineup')">LineUp</a></li>
      <li><a href="javascript:void(0)" onclick="navigateToListe('zustaendige')">Zuständige Personen</a></li>
    </ul>

    <!-- Main Content -->
    <div class="container">
      <div id="content" class="section">
        <!-- Content will be loaded here -->
      </div>
      
      <!-- Liste wird hier geladen -->
      <div id="listeContainer"></div>
    </div>

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <!-- Hauptscript -->
    <script>
      // Globale Konfiguration
      let CONFIG = null;
      
      // URL-Parameter verarbeiten
      function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }
      
      // Navigation zu einer Liste
      function navigateToListe(listType) {
        // Schließe mobile Navigation
        const sidenav = M.Sidenav.getInstance(document.getElementById('mobile-nav'));
        if (sidenav?.isOpen) sidenav.close();

        // Lade die Liste
        loadListeByType(listType);

        // Aktualisiere URL
        const url = new URL(window.location);
        url.searchParams.set('liste', listType);
        window.history.pushState({listType}, '', url);
      }
      
      // Laden der Liste basierend auf URL-Parameter
      function loadListFromUrl() {
        let listType = '<?= LISTE ?>';

        // Fallback auf clientseitige URL-Parameter
        if (!listType || listType === 'null') {
          listType = getUrlParameter('liste');
        }

        if (listType && listType !== 'null' && LIST_CONFIGS[listType]) {
          loadListeByType(listType);
        } else if (listType && listType !== 'null') {
          showError('Unbekannter Listentyp: ' + listType);
        } else {
          navigateToListe('plakate');
        }
      }
      
      // Initialisierung
      document.addEventListener('DOMContentLoaded', function() {
        google.script.run
          .withSuccessHandler(function(config) {
            CONFIG = config;

            // Initialisiere Materialize
            M.AutoInit();

            // Lade Liste basierend auf URL
            loadListFromUrl();

            // Browser-Navigation
            window.addEventListener('popstate', loadListFromUrl);
          })
          .withFailureHandler(function(error) {
            showError('Fehler beim Laden der Konfiguration');
          })
          .getConfig();
      });
    </script>
    
    <!-- Verbessertes Cache-System -->
    <?!= include('cache'); ?>
    
    <!-- UI-Hilfsfunktionen -->
    <?!= include('ui-helpers'); ?>
    
    <!-- Listen-Manager -->
    <?!= include('list-manager'); ?>
  </body>
</html> 
