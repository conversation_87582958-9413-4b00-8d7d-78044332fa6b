<script>
  // Einfaches Cache-System
  const DATA_CACHE = new Map();
  const CACHE_DURATION = 10 * 60 * 1000; // 10 Minuten

  function getCachedData(key) {
    const cached = DATA_CACHE.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  function setCachedData(key, data) {
    DATA_CACHE.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  function clearCache() {
    DATA_CACHE.clear();
  }

  function invalidateCacheForSheet(sheetName) {
    for (const [key, value] of DATA_CACHE.entries()) {
      if (value.data && value.data.sheetName === sheetName) {
        DATA_CACHE.delete(key);
      }
    }
  }

</script> 