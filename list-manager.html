<script>
  // Konfigurationen für alle Listen-Typen
  const LIST_CONFIGS = {
    // VOR MARKT - Listen mit dynamischem Hinzufügen von Einträgen
    'plakate': {
      title: 'Plakate',
      sheet: 'PLAKATE',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Ort eingeben' },  // Ort
        { editable: true, placeholder: 'Name eingeben' },  // Person
        { editable: true, placeholder: 'Status' }  // Erledigt
      ]
    },
    'kuchen': {
      title: 'Kuchenspenden',
      sheet: 'KUCHEN',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Name
        { editable: true, placeholder: 'Tag eingeben' },  // Tag
        { editable: true, placeholder: 'Info eingeben (optional)' }  // Info
      ]
    },
    'aufbau': {
      title: 'Aufbauhelfer',
      sheet: 'AUFBAU',
      type: 'vorMarkt',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Name
        { editable: true, placeholder: 'Montag' },  // Montag
        { editable: true, placeholder: 'Dienst<PERSON>' },  // Dienstag
        { editable: true, placeholder: 'Mittwoch' },  // Mittwoch
        { editable: true, placeholder: 'Donnerstag' },  // Donnerstag
        { editable: true, placeholder: 'Freitag' }   // Freitag
      ]
    },
    'zustaendige': {
      title: 'Zuständige Personen',
      sheet: 'ZUSTAENDIGE',
      type: 'sonstigeInfos',
      columns: [
        { editable: true, placeholder: 'Name eingeben' },  // Zuständige Person
        { editable: true, placeholder: 'Bereich/Aufgabe eingeben' }  // Bereich/Aufgabe
      ]
    },
  
    // WÄHREND MARKT - Schichtlisten mit festen Zeiten
    'bar': {
      title: 'Bar-Schichten',
      sheet: 'BAR',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }, // Person 2
        { editable: true, placeholder: 'Name eingeben' }, // Kaffee
        { editable: true, placeholder: 'Name eingeben' }  // Bereitschaft
      ]
    },
    'pommes': {
      title: 'Pommes-Schichten',
      sheet: 'POMMES',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'spuel': {
      title: 'Spül-Schichten',
      sheet: 'SPUEL',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'kindertoepfern': {
      title: 'Kindertöpfern (KSL)',
      sheet: 'KINDERTOEPFERN',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'lebendwerkstatt': {
      title: 'Lebendwerkstatt (KSL)',
      sheet: 'LEBENDWERKSTATT',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'siebdruck': {
      title: 'Siebdruck (KSL)',
      sheet: 'SIEBDRUCK',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }, // Person 1
        { editable: true, placeholder: 'Name eingeben' }  // Person 2
      ]
    },
    'ausstellung': {
      title: 'Ausstellungsaufsicht',
      sheet: 'AUSSTELLUNG',
      type: 'waehrendMarkt',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: true, placeholder: 'Name eingeben' }  // Person
      ]
    },
    'lineup': {
      title: 'LineUp',
      sheet: 'LINEUP',
      type: 'sonstigeInfos',
      columns: [
        { editable: false }, // Tag
        { editable: false }, // Zeit
        { editable: false }, // Künstler:innen/ Bandnamen
        { editable: false }  // Genre
      ]
    }
  };
  
  // Generische Funktion zum Laden aller Listen
  function loadListeByType(type) {
    if (!CONFIG) {
      showError('Konfiguration nicht geladen. Bitte Seite neu laden.');
      return;
    }

    const config = LIST_CONFIGS[type];
    if (!config) {
      showError('Ungültiger Listentyp');
      return;
    }

    updateUrlForList(type);
    const sheetName = CONFIG.SHEETS[config.sheet];

    // Prüfe Cache zuerst
    const cacheKey = `liste_${type}`;
    const cachedData = getCachedData(cacheKey);

    if (cachedData) {
      displayListe(cachedData, config.title, config.columns, config.type);
      return;
    }

    // Lade Daten vom Server
    loadListe(config.title, sheetName, config.columns, config.type, cacheKey);
  }
  
  // Funktion zum Aktualisieren der URL
  function updateUrlForList(listType) {
    const url = new URL(window.location);
    url.searchParams.set('liste', listType);
    window.history.pushState({}, '', url);
  }
  
  // Funktion zum Laden einer Liste
  function loadListe(title, sheetName, columnConfig, listType, cacheKey) {
    if (!sheetName || !columnConfig) {
      showError('Ungültige Parameter');
      return;
    }

    showLoading();

    google.script.run
      .withSuccessHandler(function(data) {
        if (!data || !data.data) {
          showError('Keine Daten gefunden für ' + sheetName);
          return;
        }

        // Cache die Daten
        if (cacheKey) {
          setCachedData(cacheKey, data);
        }

        displayListe(data, title, columnConfig, listType);
      })
      .withFailureHandler(function(error) {
        showError('Fehler beim Laden der Liste: ' + error);
      })
      .getListe(sheetName);
  }
  
  // Generische Funktion zum Anzeigen einer Liste
  function displayListe(data, title, columnConfig, listType) {
    const content = document.getElementById('content');
    if (!content) return;

    // Für Schichtlisten gruppieren wir nach Tagen
    if (listType === 'waehrendMarkt' || data.sheetName === CONFIG.SHEETS.LINEUP) {
      displayGroupedListe(data, title, listType, content);
    } else {
      displaySimpleListe(data, title, columnConfig, listType, content);
    }

    M.AutoInit();
  }

  // Gruppierte Listen (Schichtlisten)
  function displayGroupedListe(data, title, listType, content) {
    // Gruppiere die Daten nach Tagen
    const groupedData = {};
    data.data.forEach((row, index) => {
      const tag = row[0];
      if (!groupedData[tag]) {
        groupedData[tag] = [];
      }
      const rowWithComment = {
        data: row,
        comment: data.comments && data.comments[index] ? data.comments[index] : '',
        originalIndex: index
      };
      groupedData[tag].push(rowWithComment);
    });

    // Erstelle die HTML-Struktur
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          ${Object.entries(groupedData).map(([tag, rows]) => `
            <div class="section">
              <h5 class="teal-text">${tag}</h5>
              <div class="table-container">
                <table class="striped">
                  <thead>
                    <tr>
                      ${data.headers.slice(1).map(header => `<th>${header}</th>`).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${rows.map((rowObj, rowIndex) => {
                      const row = rowObj.data;
                      const comment = rowObj.comment;
                      const originalIndex = rowObj.originalIndex;
                      
                      let tr = document.createElement('tr');
                      tr.innerHTML = row.slice(1).map((cell, colIndex) => {
                        // Für LineUp nur Text anzeigen
                        if (data.sheetName === CONFIG.SHEETS.LINEUP) {
                          return `<td>${cell}</td>`;
                        }
                        // Zeit-Spalte ist nicht editierbar
                        if (colIndex === 0) {
                          return `<td>${cell}</td>`;
                        }
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}" 
                                   onchange="updateListe('${data.sheetName}', ${originalIndex}, ${colIndex + 1}, this.value)">
                          </div>
                        </td>`;
                      }).join('');
                      
                      let html = tr.outerHTML;
                      
                      // Füge Kommentar-Zeile hinzu, falls vorhanden (unter der Schichtzeile)
                      if (comment && data.sheetName === CONFIG.SHEETS.SPUEL) {
                        const commentRow = document.createElement('tr');
                        commentRow.className = 'comment-row';
                        commentRow.innerHTML = `
                          <td colspan="${data.headers.length - 1}" class="comment-cell">
                            <div class="chip teal lighten-4 teal-text text-darken-2">
                              <i class="material-icons tiny">info</i>
                              ${comment}
                            </div>
                          </td>
                        `;
                        html += commentRow.outerHTML;
                      }
                      
                      return html;
                    }).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  // Einfache Listen
  function displaySimpleListe(data, title, columnConfig, listType, content) {
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          <div class="table-container">
            <table class="striped">
              <thead>
                <tr>
                  ${data.headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${data.data.map((row, rowIndex) => {
                  const tr = document.createElement('tr');
                  tr.innerHTML = row.map((cell, colIndex) => {
                    // Für sonstige Infos nur Text anzeigen
                    if (listType === 'sonstigeInfos') {
                      return `<td>${cell}</td>`;
                    }
                    
                    // Für Schichtlisten (während Markt)
                    if (listType === 'waehrendMarkt') {
                      // Erste zwei Spalten (Tag und Zeit) sind nicht editierbar
                      if (colIndex < 2) {
                        return `<td>${cell}</td>`;
                      }
                      return `<td>
                        <div class="input-field">
                          <input type="text" value="${cell}" 
                                 onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                        </div>
                      </td>`;
                    }
                    
                    // Für Listen vor dem Markt
                    if (listType === 'vorMarkt') {
                      if (data.sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}" 
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                          </div>
                        </td>`;
                      }
                    }
                  }).join('');
                  return tr.outerHTML;
                }).join('')}
                ${listType === 'vorMarkt' ? `
                  <tr class="new-entry-row">
                    ${columnConfig.map((col, colIndex) => {
                      const isCheckbox = (data.sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) ||
                                        (data.sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0);

                      if (isCheckbox) {
                        return `<td class="checkbox-cell">
                          <label>
                            <input type="checkbox" class="filled-in"
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                            <span></span>
                          </label>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text"
                                   value="\\u200B"
                                   placeholder="${col.placeholder || 'Name eingeben'}"
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})"
                                   onfocus="if(this.value === '\\u200B') this.value = '';"
                                   onblur="if(this.value === '') this.value = '\\u200B';">
                          </div>
                        </td>`;
                      }
                    }).join('')}
                  </tr>
                ` : ''}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }
  
  // Generische Funktion zum Hinzufügen neuer Einträge
  function addNewEntry(sheetName, input, colIndex) {
    const row = input.closest('tr');
    const inputs = row.getElementsByTagName('input');

    // Sammle alle Werte aus der Zeile
    const values = Array.from(inputs).map(input => {
      if (input.type === 'checkbox') {
        return input.checked ? 'X' : '';
      }
      // Entferne unsichtbare Zeichen für die Speicherung
      return input.value === '\u200B' ? '' : input.value;
    });

    // Prüfe, ob die aktuelle Zeile die letzte ist
    const tbody = row.parentNode;
    const isLastRow = row === tbody.lastElementChild;

    // Wenn mindestens ein Feld ausgefüllt wurde UND es ist die letzte Zeile
    if (values.some(value => value.trim() !== '') && isLastRow) {
      // Markiere diese Zeile als "nicht mehr neu" durch Entfernen der Klasse
      row.classList.remove('new-entry-row');

      // Ändere die onchange Events zu updateListe für diese Zeile
      const currentRowIndex = Array.from(tbody.children).indexOf(row);
      Array.from(inputs).forEach((input, index) => {
        if (input.type !== 'checkbox') {
          input.setAttribute('onchange', `updateListe('${sheetName}', ${currentRowIndex}, ${index}, this.value)`);
        }
      });

      // Erstelle eine neue leere Zeile mit unsichtbaren Platzhaltern
      const newRow = document.createElement('tr');
      newRow.className = 'new-entry-row';
      newRow.innerHTML = row.innerHTML;

      // Setze unsichtbare Platzhalter in die neue Zeile
      const newInputs = newRow.getElementsByTagName('input');
      Array.from(newInputs).forEach((input, index) => {
        if (input.type !== 'checkbox') {
          input.value = '\u200B'; // Zero-width space als unsichtbarer Platzhalter
          input.setAttribute('onchange', `addNewEntry('${sheetName}', this, ${index})`);
          input.setAttribute('onfocus', `if(this.value === '\\u200B') this.value = '';`);
          input.setAttribute('onblur', `if(this.value === '') this.value = '\\u200B';`);
        } else {
          input.checked = false;
          input.setAttribute('onchange', `addNewEntry('${sheetName}', this, ${index})`);
        }
      });

      tbody.appendChild(newRow);

      // Speichere die Änderungen
      google.script.run
        .withSuccessHandler(function() {
          // Erfolgreich gespeichert
          M.toast({html: 'Eintrag gespeichert', classes: 'success'});
          // Invalidiere den Cache für diese Liste
          invalidateCacheForSheet(sheetName);
        })
        .withFailureHandler(function(error) {
          console.error('Fehler beim Hinzufügen:', error);
          showError('Fehler beim Hinzufügen: ' + error);
        })
        .addEintrag(sheetName, values);
    }
  }
  
  // Update-Funktion
  function updateListe(sheetName, rowIndex, columnIndex, value) {
    google.script.run
      .withSuccessHandler(function(success) {
        if (success) {
          M.toast({html: 'Änderung gespeichert', classes: 'success'});
          invalidateCacheForSheet(sheetName);
        } else {
          M.toast({html: 'Fehler beim Speichern', classes: 'error'});
        }
      })
      .withFailureHandler(function(error) {
        M.toast({html: 'Fehler: ' + error, classes: 'error'});
      })
      .updateListe(sheetName, rowIndex, columnIndex, value);
  }
</script> 