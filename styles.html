<style>
  /* Grundlegende Styles */
  body {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
    background-color: #f5f5f5;
  }

  main {
    flex: 1 0 auto;
  }

  /* Navigation */
  .brand-logo {
    font-size: 1.5rem !important;
    font-weight: 500;
    padding-left: 15px !important;
  }

  .sidenav {
    width: 250px;
  }

  .sidenav h5 {
    margin: 0;
    padding: 16px;
    color: #26a69a;
    font-weight: 500;
  }

  /* Dropdown Styles */
  .dropdown-content {
    background-color: #fff;
    margin: 0;
    display: none;
    min-width: 200px;
    max-height: 650px;
    overflow-y: auto;
    opacity: 0;
    position: absolute;
    z-index: 999;
    will-change: width, height;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
  }

  .dropdown-content li > a {
    color: #26a69a;
    padding: 12px 16px;
    font-size: 0.9rem;
  }

  .dropdown-content li > a:hover {
    background-color: #e0f2f1;
  }

  /* Content Styles */
  .card {
    margin: 0.5rem 0 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
  }

  .card .card-content {
    padding: 1.5rem;
  }

  .card-title {
    color: #26a69a;
    font-weight: 500;
    margin-bottom: 1rem;
  }

  /* Table Styles */
  .table-container {
    overflow-x: auto;
    margin: 0.5rem 0;
    border-radius: 4px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th {
    background-color: #26a69a;
    color: white;
    font-weight: 500;
    padding: 12px 16px;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  td, th {
    padding: 8px 16px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  tr:hover {
    background-color: #f5f5f5;
  }

  /* Form Styles */
  .input-field {
    margin: 0;
  }

  .input-field input[type=text],
  .input-field input[type=email],
  .input-field input[type=password] {
    margin: 0;
    height: 2rem !important;
    font-size: 0.9rem !important;
  }

  .input-field label {
    font-size: 0.9rem;
  }

  /* Checkboxen kompakt */
  .checkbox-cell {
    width: 35px !important;
    text-align: center !important;
    padding: 4px 2px !important;
  }

  .checkbox-cell input[type="checkbox"] {
    margin: 0 !important;
    transform: scale(0.9);
  }

  /* Kommentare */
  .comment-row {
    background-color: #f8f9fa !important;
  }

  .comment-cell {
    padding: 8px 16px !important;
  }

  .comment-cell .chip {
    margin: 0;
    font-size: 0.85rem;
  }

  /* Add Entry Row */
  .new-entry-row {
    background-color: #f8f9fa;
  }

  .new-entry-row input {
    border-bottom: 2px solid #26a69a !important;
  }

  .new-entry-row input:focus {
    border-bottom: 2px solid #26a69a !important;
    box-shadow: 0 1px 0 0 #26a69a !important;
  }

  /* Toast Messages */
  .toast.success {
    background-color: #4caf50;
  }

  .toast.error {
    background-color: #f44336;
  }

  /* Mobile Optimizations */
  @media only screen and (max-width: 600px) {
    .container {
      width: 100%;
      padding: 0 10px;
    }

    .card {
      margin: 0.5rem 0;
    }

    .card .card-content {
      padding: 1rem;
    }

    /* Mobile Table Styles */
    .table-container {
      margin: 0;
    }

    table {
      table-layout: fixed;
      width: 100%;
    }

    th, td {
      padding: 8px 4px;
      font-size: 0.85rem;
      word-wrap: break-word;
    }

    /* Touch-friendly inputs */
    input[type="text"] {
      font-size: 16px !important;
      height: 2.5rem !important;
    }

    /* Kompakte Checkboxen */
    .checkbox-cell {
      width: 20px !important;
      padding: 1px !important;
    }

    .checkbox-cell input[type="checkbox"] {
      transform: scale(0.6);
    }
  }
</style> 
