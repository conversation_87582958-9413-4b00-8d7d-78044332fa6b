// Hilfsfunktion zum Setzen der Metadaten für ein Sheet
function setSheetMetadata(sheet, teamleiter, unterueberschrift) {
  // Lösche alte Metadaten falls vorhanden
  const oldMetadata = sheet.getRange('A1:B2');
  if (oldMetadata) {
    oldMetadata.clearContent();
  }
  
  // Setze neue Metadaten
  sheet.getRange('A1:B2').setValues([
    ['Ansprechperson', teamleiter],
    ['Unterüberschrift', unterueberschrift]
  ]);
  
  // Formatiere Metadaten
  sheet.getRange('A1:B2')
    .setBackground('#f8f9fa')
    .setFontWeight('bold')
    .setHorizontalAlignment('left')
    .setVerticalAlignment('middle')
    .setWrap(true);
}

function setupSheets() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Hilfsfunktion zum Erstellen oder Aktualisieren eines Sheets
  function getOrCreateSheet(sheetName) {
    let sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      sheet = ss.insertSheet(sheetName);
    }
    return sheet;
  }
  
  // INHALTSVERZEICHNIS - Einfache Liste aller verfügbaren Listen
  let sheet = getOrCreateSheet('Inhaltsverzeichnis');
  setSheetMetadata(sheet, 'Admin', 'Übersicht aller verfügbaren Listen für die Webapp');
  sheet.getRange('A4:D4').setValues([['Listenname', 'Kategorie', 'Zuständige Person', 'Unterüberschrift']]);
  
  // Alle verfügbaren Listen
  const alleListen = [
    ['Plakate', 'Vor dem Markt', 'Dennis', 'Übersicht der verteilten Plakate und Flyer'],
    ['Kuchenspenden', 'Vor dem Markt', 'Toni', 'Wer bringt wann einen Kuchen vorbei? =)'],
    ['Aufbauhelfer', 'Vor dem Markt', '', 'WICHTIG: Treffen für Alle Helfer und Helferinnen: FR, 27.06. um 18 Uhr in Pfettrach!'],
    ['ZustaendigePersonen', 'Sonstige Infos', '', 'Übersicht der zuständigen Personen für verschiedene Bereiche'],
    ['BarSchichten', 'Während des Markts', 'Andrea', 'Je 3 Personen pro Schicht - eine Person für Bedienung der Kaffeemaschine!\n1 Person im Bereitschaftsdienst'],
    ['PommesSchichten', 'Während des Markts', 'Toni', 'Pommesparty mit Toni - 2 Personen je 2h leckker Pommes Frittes kochen!!'],
    ['SpuelSchichten', 'Während des Markts', 'Paul', 'Paul organisiert Schichten mit Spülboy-Gruppe selbstständig.\nSchichten: (je 2 Personen pro Schicht)'],
    ['Kindertoepfern (KSL)', 'Während des Markts', 'KSL', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)'],
    ['Lebendwerkstatt (KSL)', 'Während des Markts', 'KSL', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)'],
    ['Siebdruck (KSL)', 'Während des Markts', 'Pauline oder Luzie', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)'],
    ['Ausstellungsaufsicht', 'Während des Markts', '', 'Merchstand und/oder Ausstellungsraumaufsicht - wird demnächst noch geklärt.\nSchichten: (je 1 Person pro Schicht)'],
    ['LineUp', 'Sonstige Infos', 'Jakob', 'Bühnenprogramm']
  ];
  
  sheet.getRange(5, 1, alleListen.length, 4).setValues(alleListen);
  sheet.setFrozenRows(4);
  
  // VOR MARKT
  // Plakate
  sheet = getOrCreateSheet('Plakate');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:C4').setValues([['Ort', 'Person', 'Erledigt']]);
  sheet.setFrozenRows(4);
  
  // Kuchenspenden
  sheet = getOrCreateSheet('Kuchenspenden');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:C4').setValues([['Name', 'Tag', 'Info (optional)']]);
  sheet.setFrozenRows(4);
  
  // Aufbauhelfer
  sheet = getOrCreateSheet('Aufbauhelfer');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:F4').setValues([['Name', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag']]);
  sheet.setFrozenRows(4);
  
  // Zuständige Personen
  sheet = getOrCreateSheet('ZustaendigePersonen');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:B4').setValues([['Zuständige Person', 'Bereich/Aufgabe']]);
  sheet.setFrozenRows(4);
  
  // WÄHREND MARKT
  // Bar Schichten
  sheet = getOrCreateSheet('BarSchichten');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:F4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2', 'Kaffee', 'Bereitschaft']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Pommes Schichten
  sheet = getOrCreateSheet('PommesSchichten');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Spül Schichten
  sheet = getOrCreateSheet('SpuelSchichten');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:E4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2', 'Aufgabenbereich der Schicht']]);
  sheet.getRange('A5:A12').setValues([
    ['Freitag'],
    ['Freitag'],
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B12').setValues([
    ['11:30-14:00'],
    ['16:00-20:00'],
    ['09:30-13:30'],
    ['13:30-17:30'],
    ['17:30- Ende'],
    ['09:30-13:30'],
    ['13:30-17:30'],
    ['17:30 - Ende']
  ]);
  
  // Füge Aufgaben als Kommentare zu den Zeilen hinzu
  sheet.getRange('A5').setNote('Hänger holen und einrichten');
  sheet.getRange('A6').setNote('Aufbau, Geschirr durchspülen und evtl Abendgeschirr');
  sheet.getRange('A7').setNote('Reste vom Vortag und Spülen, Gläser zu Bar bringen');
  sheet.getRange('A8').setNote('Spülen');
  sheet.getRange('A9').setNote('Spülen, Geschirr für Ausstelleressen, Säubern (Spülmaschine und Überlaufwanne)');
  sheet.getRange('A10').setNote('Reste vom Vortag und Spülen');
  sheet.getRange('A11').setNote('Spülen und ggf. schon mal schauen ob man Kuchenplatten usw zurückführen kann damit später genügend Platz ist');
  sheet.getRange('A12').setNote('Alles was zurückkommt vom Kuchenverkauf. Pommesfriteuse etc, Geschirr sortieren, Spülmaschinen säubern und Hänger zurückbauen und für Transport fertig machen.');
  
  // Füge Montag als separate Zeile hinzu
  sheet.getRange('A13').setValue('Montag');
  sheet.getRange('A13').setNote('Hänger zurück bringen');
  
  sheet.setFrozenRows(4);
  
  // Kindertöpfern
  sheet = getOrCreateSheet('Kindertoepfern');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Lebendwerkstatt
  sheet = getOrCreateSheet('Lebendwerkstatt');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Siebdruck
  sheet = getOrCreateSheet('Siebdruck');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Ausstellungsaufsicht
  sheet = getOrCreateSheet('Ausstellungsaufsicht');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:C4').setValues([['Tag', 'Zeit', 'Person']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // SONSTIGE INFOS
  // LineUp
  sheet = getOrCreateSheet('LineUp');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'KünstlerIn(nen)', 'Genre']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['12:00 - 13:00 Uhr'], ['13:30 - 14:30 Uhr'], ['15:00 - 16:00 Uhr'], ['16:30 - 17:30 Uhr']]);
  sheet.getRange('B9:B12').setValues([['12:00 - 13:00 Uhr'], ['13:30 - 14:30 Uhr'], ['15:00 - 16:00 Uhr'], ['16:30 - 17:30 Uhr']]);
  sheet.getRange('C5:C12').setValues([
    ['Flori Zigori'],
    ['Hanging Lamp Trio'],
    ['Trattoria Tristezza'],
    ['Romeo Hall und Stefan Kirner'],
    ['Kaseko Projekt'],
    ['Wollstiefel'],
    ['Walther, angenehm'],
    ['Fraenko']
  ]);
  sheet.getRange('D5:D12').setValues([
    ['Outlaw guitar songs (Singer/Songwriter)'],
    ['Jam Band'],
    ['Chanson-Punk'],
    ['Singer Songwriter; Dark Country'],
    ['Jazz'],
    ['Akustikpunk mit Gitarre und Cajon'],
    ['Psychedelic Rock / Hip Hop / Jazz'],
    ['R&B, Hip Hop und Vibes']
  ]);
  sheet.setFrozenRows(4);
  
  // Formatiere alle Sheets
  const sheets = ss.getSheets();
  sheets.forEach(sheet => {
    const lastColumn = sheet.getLastColumn();
    if (lastColumn > 0) {
      // Setze Spaltenbreiten
      sheet.setColumnWidths(1, lastColumn, 150);
      
      // Formatiere Header
      const headerRange = sheet.getRange(4, 1, 1, lastColumn);
      headerRange.setBackground('#2c3e50')
                 .setFontColor('white')
                 .setFontWeight('bold');
      
      // Formatiere Zellen
      const lastRow = sheet.getLastRow();
      if (lastRow > 4) {
        const dataRange = sheet.getRange(5, 1, lastRow - 4, lastColumn);
        dataRange.setHorizontalAlignment('left')
                 .setVerticalAlignment('middle')
                 .setWrap(true);
      }
    }
  });
  
  Logger.log('Sheets erfolgreich eingerichtet');
}

// Neue Funktion: Übertrage zuständige Personen und Unterüberschriften aus dem Inhaltsverzeichnis
function updateZustaendigePersonen() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const inhaltsverzeichnis = ss.getSheetByName('Inhaltsverzeichnis');
  
  if (!inhaltsverzeichnis) {
    Logger.log('Inhaltsverzeichnis nicht gefunden');
    return;
  }
  
  try {
    const dataRange = inhaltsverzeichnis.getDataRange();
    const values = dataRange.getValues();
    
    // Überspringe die ersten 4 Zeilen (Metadaten und Header)
    const data = values.slice(4);
    
    data.forEach(row => {
      const listenname = row[0];
      const zustaendigePerson = row[2]; // Spalte C (Index 2)
      const unterueberschrift = row[3]; // Spalte D (Index 3)
      
      // Mappe den Listenname auf den entsprechenden Sheet-Namen
      let sheetName = '';
      switch (listenname) {
        case 'Plakate':
          sheetName = 'Plakate';
          break;
        case 'Kuchenspenden':
          sheetName = 'Kuchenspenden';
          break;
        case 'Aufbauhelfer':
          sheetName = 'Aufbauhelfer';
          break;
        case 'ZustaendigePersonen':
          sheetName = 'ZustaendigePersonen';
          break;
        case 'BarSchichten':
          sheetName = 'BarSchichten';
          break;
        case 'PommesSchichten':
          sheetName = 'PommesSchichten';
          break;
        case 'SpuelSchichten':
          sheetName = 'SpuelSchichten';
          break;
        case 'Kindertoepfern (KSL)':
          sheetName = 'Kindertoepfern';
          break;
        case 'Lebendwerkstatt (KSL)':
          sheetName = 'Lebendwerkstatt';
          break;
        case 'Siebdruck (KSL)':
          sheetName = 'Siebdruck';
          break;
        case 'Ausstellungsaufsicht':
          sheetName = 'Ausstellungsaufsicht';
          break;
        case 'LineUp':
          sheetName = 'LineUp';
          break;
      }
      
      if (sheetName) {
        const sheet = ss.getSheetByName(sheetName);
        if (sheet) {
          // Aktualisiere die zuständige Person in Zeile 1, Spalte B
          if (zustaendigePerson && zustaendigePerson.trim() !== '') {
            sheet.getRange('B1').setValue(zustaendigePerson);
            Logger.log(`Zuständige Person für ${listenname} aktualisiert: ${zustaendigePerson}`);
          }
          
          // Aktualisiere die Unterüberschrift in Zeile 2, Spalte B
          if (unterueberschrift && unterueberschrift.trim() !== '') {
            sheet.getRange('B2').setValue(unterueberschrift);
            Logger.log(`Unterüberschrift für ${listenname} aktualisiert: ${unterueberschrift}`);
          }
        }
      }
    });
    
    Logger.log('Alle zuständigen Personen und Unterüberschriften erfolgreich aktualisiert');
  } catch (error) {
    Logger.log('Fehler beim Aktualisieren der zuständigen Personen und Unterüberschriften: ' + error);
  }
}

// Menü erstellen
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('ListenMarkt')
    .addSubMenu(ui.createMenu('Vor dem Markt')
      .addItem('Plakate', 'openPlakate')
      .addItem('Kuchenspenden', 'openKuchenspenden')
      .addItem('Aufbauhelfer', 'openAufbauhelfer'))
    .addSubMenu(ui.createMenu('Während des Markts')
      .addItem('Bar-Schichten', 'openBarSchichten')
      .addItem('Pommes-Schichten', 'openPommesSchichten')
      .addItem('Spül-Schichten', 'openSpuelSchichten')
      .addItem('Kindertöpfern (KSL)', 'openKindertoepfern')
      .addItem('Lebendwerkstatt (KSL)', 'openLebendwerkstatt')
      .addItem('Siebdruck (KSL)', 'openSiebdruck')
      .addItem('Ausstellungsaufsicht', 'openAusstellungsaufsicht'))
    .addSubMenu(ui.createMenu('Sonstige Infos')
      .addItem('LineUp', 'openLineUp')
      .addItem('Zuständige Personen', 'openZustaendigePersonen'))
    .addSeparator()
    .addSubMenu(ui.createMenu('Admin')
      .addItem('Alle Sheets löschen', 'deleteAllSheets')
      .addItem('Sheets neu erstellen', 'resetAllSheets')
      .addItem('Zuständige Personen aktualisieren', 'updateZustaendigePersonen'))
    .addToUi();
}

// Funktionen zum Öffnen der einzelnen Sheets
function openPlakate() { openSheet('Plakate'); }
function openKuchenspenden() { openSheet('Kuchenspenden'); }
function openAufbauhelfer() { openSheet('Aufbauhelfer'); }
function openBarSchichten() { openSheet('BarSchichten'); }
function openPommesSchichten() { openSheet('PommesSchichten'); }
function openSpuelSchichten() { openSheet('SpuelSchichten'); }
function openKindertoepfern() { openSheet('Kindertoepfern'); }
function openLebendwerkstatt() { openSheet('Lebendwerkstatt'); }
function openSiebdruck() { openSheet('Siebdruck'); }
function openAusstellungsaufsicht() { openSheet('Ausstellungsaufsicht'); }
function openLineUp() { openSheet('LineUp'); }
function openZustaendigePersonen() { openSheet('ZustaendigePersonen'); }

// Hilfsfunktion zum Öffnen eines Sheets
function openSheet(sheetName) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  if (sheet) {
    ss.setActiveSheet(sheet);
    sheet.getRange('A1').activate();
  } else {
    SpreadsheetApp.getUi().alert(`Sheet "${sheetName}" nicht gefunden!`);
  }
}

// Alle Sheets löschen (außer Tabellenblatt1)
function deleteAllSheets() {
  const ui = SpreadsheetApp.getUi();
  const result = ui.alert(
    'Alle Sheets löschen',
    'Möchtest du wirklich alle Sheets löschen? Diese Aktion kann nicht rückgängig gemacht werden!',
    ui.ButtonSet.YES_NO
  );
  
  if (result === ui.Button.YES) {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = ss.getSheets();
    
    // Erstelle Tabellenblatt1 falls nicht vorhanden
    let tabellenblatt1 = ss.getSheetByName('Tabellenblatt1');
    if (!tabellenblatt1) {
      tabellenblatt1 = ss.insertSheet('Tabellenblatt1');
    }
    
    // Lösche alle anderen Sheets
    sheets.forEach(sheet => {
      if (sheet.getName() !== 'Tabellenblatt1') {
        ss.deleteSheet(sheet);
      }
    });
    
    ui.alert('Alle Sheets wurden gelöscht!');
  }
}

// Alle Sheets neu erstellen
function resetAllSheets() {
  const ui = SpreadsheetApp.getUi();
  const result = ui.alert(
    'Sheets neu erstellen',
    'Möchtest du alle Sheets löschen und neu erstellen? Alle Daten gehen verloren!',
    ui.ButtonSet.YES_NO
  );
  
  if (result === ui.Button.YES) {
    try {
      // Erstelle Tabellenblatt1 falls nicht vorhanden
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      let tabellenblatt1 = ss.getSheetByName('Tabellenblatt1');
      if (!tabellenblatt1) {
        tabellenblatt1 = ss.insertSheet('Tabellenblatt1');
      }
      
      // Lösche alle anderen Sheets
      const sheets = ss.getSheets();
      sheets.forEach(sheet => {
        if (sheet.getName() !== 'Tabellenblatt1') {
          ss.deleteSheet(sheet);
        }
      });
      
      // Erstelle alle Sheets neu
      setupSheets();
      
      // Aktualisiere zuständige Personen
      updateZustaendigePersonen();
      
      // Lösche Tabellenblatt1
      tabellenblatt1 = ss.getSheetByName('Tabellenblatt1');
      if (tabellenblatt1) {
        ss.deleteSheet(tabellenblatt1);
      }
      
      ui.alert('Alle Sheets wurden erfolgreich neu erstellt!');
      
    } catch (error) {
      ui.alert('Fehler beim Neuerstellen der Sheets: ' + error.toString());
    }
  }
} 